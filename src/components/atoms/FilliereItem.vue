<template>
    <li class="flex items-center justify-between p-4 border-b border-gray-200 hover:bg-gray-50 transition-colors">
        <!-- Contenu principal -->
        <div class="flex-1">
            <h3 class="text-sm font-medium text-gray-900">{{ title }}</h3>
            <p class="text-xs text-gray-500 mt-1">ID: {{ id }}</p>
        </div>

        <!-- Menu d'actions -->
        <DropdownMenu>
            <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="icon" class="h-8 w-8 text-gray-500 hover:text-gray-700"
                    aria-label="Plus d'options">
                    <span class="iconify hugeicons--more-horizontal" aria-hidden="true"></span>
                </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" class="w-48">
                <DropdownMenuItem @click="handleEdit" class="cursor-pointer">
                    <span class="iconify hugeicons--edit-02 mr-2" aria-hidden="true"></span>
                    Modifier
                </DropdownMenuItem>

                <DropdownMenuItem @click="handleDelete" variant="destructive"
                    class="cursor-pointer text-red-600 focus:text-red-600">
                    <span class="iconify hugeicons--delete-02 mr-2" aria-hidden="true"></span>
                    Supprimer
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    </li>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Props du composant
const props = defineProps<{
    /** Titre de l'élément de la filière */
    title: string
    /** Identifiant unique de l'élément */
    id: string | number
}>()

// Événements émis par le composant
const emit = defineEmits<{
    /** Émis quand l'utilisateur clique sur modifier */
    edit: [id: string | number]
    /** Émis quand l'utilisateur clique sur supprimer */
    delete: [id: string | number]
}>()

// Gestionnaires d'événements
const handleEdit = () => {
    emit('edit', props.id)
}

const handleDelete = () => {
    emit('delete', props.id)
}
</script>

<style scoped>
/* Styles spécifiques au composant si nécessaire */
</style>