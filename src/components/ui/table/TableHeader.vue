<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <thead
    data-slot="table-header"
    :class="cn('[&_tr]:border-b [&_th]:bg-primary [&_th]:first:rounded-tl-xl [&_th]:last:rounded-tr-xl [&_th]:text-white', props.class)"
  >
    <slot />
  </thead>
</template>
