<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { tagNavSaisie } from './tags-nav';

import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'



</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisie" active-tag-name="cours" />
            <div
                class="mt-10 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-2 gap-4 md:gap-10">
                <Table class="rounded-xl bg-white">
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[20px]">
                                <Checkbox class="bg-white scale-70" />
                            </TableHead>
                            <TableHead class="text-left">
                                Code
                            </TableHead>
                            <TableHead>
                                Designation
                            </TableHead>
                            <TableHead>
                                Niveau
                            </TableHead>
                            <TableHead>
                                Filière
                            </TableHead>
                            <TableHead>
                                Cycle
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>

                    </TableBody>
                </Table>

                <div class="overflow-x-auto rounded-xl shadow-md">
                    <table class="min-w-full text-sm text-left border-collapse">
                        <!-- Table Header -->
                        <thead class="bg-blue-500 text-white text-xs uppercase">
                            <tr>
                                <th class="p-4"><input type="checkbox" /></th>
                                <th class="p-4">Code</th>
                                <th class="p-4">Intitulé du cours</th>
                                <th class="p-4">Niveau</th>
                                <th class="p-4">Filière</th>
                                <th class="p-4">Titulaire</th>
                                <th class="p-4">Volume horaire</th>
                                <th class="p-4" colspan="5" align="center">Maximum</th>
                            </tr>
                            <tr class="bg-blue-500 text-white text-xs uppercase">
                                <th colspan="7"></th>
                                <th class="p-2">P1</th>
                                <th class="p-2">P2</th>
                                <th class="p-2">E1</th>
                                <th class="p-2">P3</th>
                                <th class="p-2">P4</th>
                                <th class="p-2">E2</th>
                            </tr>
                        </thead>

                        <!-- Table Body -->
                        <tbody class="bg-white">
                            <tr class="border-t">
                                <td class="p-4"><input type="checkbox" checked /></td>
                                <td class="p-4">7300</td>
                                <td class="p-4">Autres articles similaires, non filetés, en fonte, fer ou acier</td>
                                <td class="p-4">2</td>
                                <td class="p-4">Letter</td>
                                <td class="p-4">Kalala</td>
                                <td class="p-4">50h</td>
                                <td class="p-4">73</td>
                                <td class="p-4">30</td>
                                <td class="p-4">68</td>
                                <td class="p-4">83</td>
                                <td class="p-4">76</td>
                                <td class="p-4">57</td>
                            </tr>
                            <tr class="border-t">
                                <td class="p-4"><input type="checkbox" checked /></td>
                                <td class="p-4">7300</td>
                                <td class="p-4">Autres articles similaires, non filetés, en fonte, fer ou acier</td>
                                <td class="p-4">3</td>
                                <td class="p-4">Langues</td>
                                <td class="p-4">Kilolo</td>
                                <td class="p-4">30h</td>
                                <td class="p-4">73</td>
                                <td class="p-4">40</td>
                                <td class="p-4">67</td>
                                <td class="p-4">65</td>
                                <td class="p-4">76</td>
                                <td class="p-4">76</td>
                            </tr>
                            <tr class="border-t">
                                <td class="p-4"><input type="checkbox" /></td>
                                <td class="p-4">7300</td>
                                <td class="p-4">Autres articles similaires, non filetés, en fonte, fer ou acier</td>
                                <td class="p-4">1</td>
                                <td class="p-4">Art</td>
                                <td class="p-4">Ngoie</td>
                                <td class="p-4">20h</td>
                                <td class="p-4">67</td>
                                <td class="p-4">75</td>
                                <td class="p-4">43</td>
                                <td class="p-4">40</td>
                                <td class="p-4">98</td>
                                <td class="p-4">70</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </DashLayout>
</template>
