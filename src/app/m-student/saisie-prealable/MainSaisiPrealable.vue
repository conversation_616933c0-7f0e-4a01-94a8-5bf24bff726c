<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Input } from '@/components/ui/input';


const tags = [
    {
        name: "filieres",
        text: "Filières",
        href: "/apprenants/saisie-prealable/filieres"
    },
    {
        name: "classes",
        text: "Classes",
        href: "/apprenants/saisie-prealable/classes"
    },
    {
        name: "cours",
        text: "Cours",
        href: "/apprenants/saisie-prealable/cours"
    },
]



const filieres = [
    {
        id: 1,
        text: "Specialisations en arts"
    },
    {
        id: 2,
        text: "Lettres"
    },
    {
        id: 3,
        text: "Langues"
    },
]
</script>

<template>
    <DashLayout active-route="/apprenants" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tags" active-tag-name="filieres" />
            <div class="mt-10 bg-white rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-2 gap-4">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="relative">
                        <Input type="text" id="username" name="username" placeholder="utilisateur"
                            class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-9 rounded-full" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <ul class="flex flex-col">

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
